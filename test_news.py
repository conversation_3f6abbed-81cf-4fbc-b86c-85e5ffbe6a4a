# test_news.py
import asyncio
from fastmcp import Client
import mediastack_server


async def test():
    client = Client(mediastack_server.mcp)
    async with client:
        # Test API status
        status = await client.call_tool("check_api_status", {})
        print("API Status:", status[0].text)

        # Test news search
        news = await client.call_tool(
            "get_live_news", {"categories": "technology", "limit": 5}
        )
        print("Tech News:", news[0].text)


asyncio.run(test())
