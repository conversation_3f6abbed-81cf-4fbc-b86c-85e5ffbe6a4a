# Mediastack News MCP Server

A FastMCP server for accessing live news data from Mediastack API.

## Setup

1. Get free API key: https://mediastack.com/signup/free
2. Set environment variable: `MEDIASTACK_API_KEY=your_key`
3. Install: `pip install -r requirements.txt`
4. Run: `python mediastack_server.py`

## Features

-   Live news with filtering
-   Keyword search
-   Category browsing
-   Source discovery
-   API status checking
