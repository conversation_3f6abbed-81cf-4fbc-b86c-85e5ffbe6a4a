startCommand:
    type: stdio
    configSchema:
        type: object
        required: ["apiKey"]
        properties:
            apiKey:
                type: string
                title: "Mediastack API Key"
                description: "Get your free API key from https://mediastack.com/signup/free"
            timeout:
                type: number
                title: "API Timeout (seconds)"
                default: 10
                minimum: 5
                maximum: 30
    commandFunction: |-
        (config) => ({
          "command": "python",
          "args": ["mediastack_server.py"],
          "env": {
            "MEDIASTACK_API_KEY": config.apiKey,
            "MEDIASTACK_API_TIMEOUT": config.timeout ? config.timeout.toString() : "10"
          }
        })
