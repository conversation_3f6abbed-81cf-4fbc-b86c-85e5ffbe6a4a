#!/usr/bin/env python3
"""
Mediastack News API FastMCP Server

A production-ready FastMCP server that provides access to live news data
from the Mediastack API with comprehensive filtering and search capabilities.
"""

from fastmcp import FastMCP, Context
from fastmcp.exceptions import ToolError
import httpx
import os
from datetime import datetime
from typing import Dict, Optional, List, Any
import asyncio
from pydantic import Field
from typing import Annotated

# Initialize the FastMCP server
mcp = FastMCP(
    name="MediastackMCP",
    instructions="""
    This server provides access to live news data from the Mediastack API.
    
    Available tools:
    - get_live_news: Fetch current news articles with comprehensive filtering
    - search_news_by_keyword: Quick keyword-based news search
    - get_news_by_category: Get news filtered by specific categories
    - get_news_sources: Search and discover available news sources
    - check_api_status: Test API connectivity and configuration
    
    Available resources:
    - config://mediastack-api: API configuration and setup information
    - data://news-categories: Available news categories and examples
    - data://supported-countries: Country codes for news filtering
    - data://supported-languages: Language codes for news filtering
    """,
)

# API Configuration
MEDIASTACK_BASE_URL = "http://api.mediastack.com/v1"
API_KEY = os.getenv("MEDIASTACK_API_KEY")
API_TIMEOUT = int(os.getenv("MEDIASTACK_API_TIMEOUT", "10"))

# News categories available in Mediastack
NEWS_CATEGORIES = [
    "general",
    "business",
    "entertainment",
    "health",
    "science",
    "sports",
    "technology",
]

# Language codes supported by Mediastack
SUPPORTED_LANGUAGES = {
    "ar": "Arabic",
    "de": "German",
    "en": "English",
    "es": "Spanish",
    "fr": "French",
    "he": "Hebrew",
    "it": "Italian",
    "nl": "Dutch",
    "no": "Norwegian",
    "pt": "Portuguese",
    "ru": "Russian",
    "se": "Swedish",
    "zh": "Chinese",
}

# Common country codes (Mediastack supports many more)
SUPPORTED_COUNTRIES = {
    "us": "United States",
    "gb": "United Kingdom",
    "ca": "Canada",
    "au": "Australia",
    "de": "Germany",
    "fr": "France",
    "it": "Italy",
    "es": "Spain",
    "jp": "Japan",
    "cn": "China",
    "in": "India",
    "br": "Brazil",
    "mx": "Mexico",
    "ru": "Russia",
    "za": "South Africa",
    "nl": "Netherlands",
    "se": "Sweden",
    "no": "Norway",
    "dk": "Denmark",
}


async def make_api_request(endpoint: str, params: Dict[str, Any]) -> Dict[str, Any]:
    """
    Make an authenticated API request to Mediastack API with proper error handling.

    Args:
        endpoint: API endpoint (e.g., 'news', 'sources')
        params: Query parameters for the request

    Returns:
        API response data as dictionary

    Raises:
        ToolError: For various API and network errors
    """
    if not API_KEY:
        raise ToolError(
            "API key not configured. Please set MEDIASTACK_API_KEY environment variable. "
            + "Get your free API key at: https://mediastack.com/signup/free"
        )

    # Add API key to parameters
    params["access_key"] = API_KEY

    async with httpx.AsyncClient(timeout=API_TIMEOUT) as client:
        try:
            response = await client.get(
                f"{MEDIASTACK_BASE_URL}/{endpoint}", params=params
            )
            response.raise_for_status()
            data = response.json()

            # Check for API-specific errors
            if "error" in data:
                error_info = data["error"]
                error_code = error_info.get("code", "unknown_error")
                error_message = error_info.get("message", "Unknown API error")

                # Provide helpful error messages based on error code
                if error_code == "invalid_access_key":
                    raise ToolError(
                        "Invalid API key. Please check your MEDIASTACK_API_KEY."
                    )
                elif error_code == "missing_access_key":
                    raise ToolError(
                        "API key is required. Please set MEDIASTACK_API_KEY environment variable."
                    )
                elif error_code == "usage_limit_reached":
                    raise ToolError(
                        "Monthly API usage limit reached. Consider upgrading your plan."
                    )
                elif error_code == "rate_limit_reached":
                    raise ToolError(
                        "API rate limit exceeded. Please wait before making more requests."
                    )
                elif error_code == "function_access_restricted":
                    raise ToolError(
                        "This feature requires a paid plan. Current plan doesn't support this endpoint."
                    )
                else:
                    raise ToolError(f"API Error ({error_code}): {error_message}")

            return data

        except httpx.TimeoutException:
            raise ToolError(
                f"Request timed out after {API_TIMEOUT} seconds. Try again later."
            )
        except httpx.HTTPStatusError as e:
            if e.response.status_code == 404:
                raise ToolError("API endpoint not found. Please check your request.")
            elif e.response.status_code == 401:
                raise ToolError("Unauthorized access. Please check your API key.")
            elif e.response.status_code == 429:
                raise ToolError("Too many requests. Please wait before trying again.")
            else:
                raise ToolError(
                    f"HTTP Error {e.response.status_code}: {e.response.text}"
                )
        except httpx.RequestError as e:
            raise ToolError(
                f"Network error: {str(e)}. Please check your internet connection."
            )
        except Exception as e:
            raise ToolError(f"Unexpected error: {str(e)}")


def format_news_article(article: Dict[str, Any]) -> Dict[str, Any]:
    """Format a news article for consistent output."""
    return {
        "title": article.get("title", "No title available"),
        "author": article.get("author", "Unknown author"),
        "description": article.get("description", "No description available"),
        "url": article.get("url", ""),
        "source": article.get("source", "Unknown source"),
        "category": article.get("category", "general"),
        "country": article.get("country", "unknown"),
        "language": article.get("language", "unknown"),
        "published_at": article.get("published_at", ""),
        "image": article.get("image", ""),
        "formatted_date": format_publish_date(article.get("published_at", "")),
    }


def format_publish_date(date_string: str) -> str:
    """Format publication date for better readability."""
    if not date_string:
        return "Unknown date"

    try:
        # Parse ISO format: 2020-08-05T05:47:24+00:00
        dt = datetime.fromisoformat(date_string.replace("Z", "+00:00"))
        return dt.strftime("%B %d, %Y at %I:%M %p UTC")
    except:
        return date_string


@mcp.tool()
async def get_live_news(
    keywords: Annotated[
        Optional[str],
        Field(
            description="Search keywords. Separate multiple keywords with commas. Use '-' prefix to exclude (e.g., 'bitcoin,-crypto')"
        ),
    ] = None,
    sources: Annotated[
        Optional[str],
        Field(
            description="News sources to include/exclude. Use source IDs separated by commas. Use '-' prefix to exclude (e.g., 'cnn,-bbc')"
        ),
    ] = None,
    categories: Annotated[
        Optional[str],
        Field(
            description="Categories: general, business, entertainment, health, science, sports, technology. Use '-' to exclude (e.g., 'business,-sports')"
        ),
    ] = None,
    countries: Annotated[
        Optional[str],
        Field(
            description="Country codes (e.g., 'us,gb,ca'). Use '-' to exclude (e.g., 'us,-gb')"
        ),
    ] = None,
    languages: Annotated[
        Optional[str],
        Field(
            description="Language codes (e.g., 'en,es,fr'). Use '-' to exclude (e.g., 'en,-de')"
        ),
    ] = None,
    sort: Annotated[
        str,
        Field(
            description="Sort order: 'published_desc' (newest first), 'published_asc' (oldest first), 'popularity'"
        ),
    ] = "published_desc",
    limit: Annotated[
        int, Field(description="Number of articles to return (1-100)", ge=1, le=100)
    ] = 25,
) -> Dict[str, Any]:
    """
    Fetch live news articles with comprehensive filtering options.

    Returns current news articles from Mediastack API with pagination info.
    Free plan users receive news with a 30-minute delay.

    Example usage:
    - All news: get_live_news()
    - Tech news: get_live_news(categories="technology")
    - US business news: get_live_news(categories="business", countries="us")
    - Search with exclusions: get_live_news(keywords="climate,-politics")
    """

    params = {
        "sort": sort,
        "limit": limit,
        "offset": 0,  # Start from beginning for live news
    }

    # Add optional filters
    if keywords:
        params["keywords"] = keywords
    if sources:
        params["sources"] = sources
    if categories:
        params["categories"] = categories
    if countries:
        params["countries"] = countries
    if languages:
        params["languages"] = languages

    try:
        data = await make_api_request("news", params)

        # Format articles for better presentation
        articles = [format_news_article(article) for article in data.get("data", [])]

        return {
            "success": True,
            "articles": articles,
            "pagination": data.get("pagination", {}),
            "total_results": data.get("pagination", {}).get("total", 0),
            "filters_applied": {
                "keywords": keywords,
                "sources": sources,
                "categories": categories,
                "countries": countries,
                "languages": languages,
                "sort": sort,
            },
            "timestamp": datetime.now().isoformat(),
            "note": "Free plan users receive news with 30-minute delay",
        }

    except ToolError:
        raise
    except Exception as e:
        raise ToolError(f"Failed to fetch news: {str(e)}")


@mcp.tool()
async def search_news_by_keyword(
    keyword: Annotated[str, Field(description="Main search keyword or phrase")],
    exclude_keywords: Annotated[
        Optional[str], Field(description="Keywords to exclude (comma-separated)")
    ] = None,
    language: Annotated[
        Optional[str], Field(description="Language code (e.g., 'en', 'es', 'fr')")
    ] = "en",
    limit: Annotated[
        int, Field(description="Number of results (1-50)", ge=1, le=50)
    ] = 15,
) -> Dict[str, Any]:
    """
    Quick keyword-based news search with optional exclusions.

    Simplified interface for searching news by specific keywords.
    Perfect for finding articles about specific topics or events.

    Example usage:
    - search_news_by_keyword("artificial intelligence")
    - search_news_by_keyword("climate change", exclude_keywords="politics,opinion")
    """

    # Build search string
    search_terms = keyword
    if exclude_keywords:
        excluded = [f"-{term.strip()}" for term in exclude_keywords.split(",")]
        search_terms += "," + ",".join(excluded)

    params = {
        "keywords": search_terms,
        "languages": language,
        "sort": "published_desc",
        "limit": limit,
        "offset": 0,
    }

    try:
        data = await make_api_request("news", params)
        articles = [format_news_article(article) for article in data.get("data", [])]

        return {
            "success": True,
            "search_keyword": keyword,
            "excluded_keywords": exclude_keywords,
            "language": language,
            "articles": articles,
            "results_count": len(articles),
            "total_available": data.get("pagination", {}).get("total", 0),
            "timestamp": datetime.now().isoformat(),
        }

    except ToolError:
        raise
    except Exception as e:
        raise ToolError(f"Keyword search failed: {str(e)}")


@mcp.tool()
async def get_news_by_category(
    category: Annotated[
        str,
        Field(
            description="News category: general, business, entertainment, health, science, sports, technology"
        ),
    ],
    country: Annotated[
        Optional[str],
        Field(description="Country code for regional news (e.g., 'us', 'gb', 'ca')"),
    ] = None,
    language: Annotated[
        Optional[str], Field(description="Language code (e.g., 'en', 'es', 'fr')")
    ] = None,
    limit: Annotated[
        int, Field(description="Number of articles (1-50)", ge=1, le=50)
    ] = 20,
) -> Dict[str, Any]:
    """
    Get news articles filtered by specific category.

    Optimized for browsing news by topic with optional geographic filtering.

    Example usage:
    - get_news_by_category("business")
    - get_news_by_category("technology", country="us")
    - get_news_by_category("sports", country="gb", language="en")
    """

    # Validate category
    if category not in NEWS_CATEGORIES:
        raise ToolError(
            f"Invalid category '{category}'. Available categories: {', '.join(NEWS_CATEGORIES)}"
        )

    params = {
        "categories": category,
        "sort": "published_desc",
        "limit": limit,
        "offset": 0,
    }

    if country:
        params["countries"] = country
    if language:
        params["languages"] = language

    try:
        data = await make_api_request("news", params)
        articles = [format_news_article(article) for article in data.get("data", [])]

        return {
            "success": True,
            "category": category,
            "country_filter": country,
            "language_filter": language,
            "articles": articles,
            "results_count": len(articles),
            "total_available": data.get("pagination", {}).get("total", 0),
            "timestamp": datetime.now().isoformat(),
        }

    except ToolError:
        raise
    except Exception as e:
        raise ToolError(f"Category search failed: {str(e)}")


@mcp.tool()
async def get_news_sources(
    search_term: Annotated[
        str,
        Field(
            description="Search term for finding news sources (e.g., 'BBC', 'CNN', 'tech')"
        ),
    ],
    country: Annotated[
        Optional[str], Field(description="Filter by country code (e.g., 'us', 'gb')")
    ] = None,
    language: Annotated[
        Optional[str], Field(description="Filter by language code (e.g., 'en', 'es')")
    ] = None,
    category: Annotated[
        Optional[str],
        Field(description="Filter by category (e.g., 'business', 'technology')"),
    ] = None,
    limit: Annotated[
        int, Field(description="Number of sources to return (1-50)", ge=1, le=50)
    ] = 25,
) -> Dict[str, Any]:
    """
    Search for available news sources in the Mediastack database.

    Find news sources by name, country, language, or category.
    Returns source IDs that can be used in other news queries.

    Example usage:
    - get_news_sources("BBC")
    - get_news_sources("tech", category="technology")
    - get_news_sources("news", country="us", language="en")
    """

    params = {"search": search_term, "limit": limit, "offset": 0}

    if country:
        params["countries"] = country
    if language:
        params["languages"] = language
    if category:
        params["categories"] = category

    try:
        data = await make_api_request("sources", params)

        # Format sources for easy use
        sources = []
        for source in data.get("data", []):
            formatted_source = {
                "id": source.get("id", ""),
                "name": source.get("name", "Unknown"),
                "category": source.get("category", "general"),
                "country": source.get("country", "unknown"),
                "language": source.get("language", "unknown"),
                "url": source.get("url", ""),
                "usage_example": f"Use 'sources={source.get('id', '')}' in news queries",
            }
            sources.append(formatted_source)

        return {
            "success": True,
            "search_term": search_term,
            "filters": {"country": country, "language": language, "category": category},
            "sources": sources,
            "results_count": len(sources),
            "total_available": data.get("pagination", {}).get("total", 0),
            "timestamp": datetime.now().isoformat(),
        }

    except ToolError:
        raise
    except Exception as e:
        raise ToolError(f"Source search failed: {str(e)}")


@mcp.tool()
async def check_api_status() -> Dict[str, Any]:
    """
    Test API connectivity and configuration status.

    Verifies that the API key is working and provides diagnostic information.
    Useful for troubleshooting connection issues.
    """

    status_info = {
        "timestamp": datetime.now().isoformat(),
        "api_key_configured": bool(API_KEY),
        "api_timeout": API_TIMEOUT,
        "base_url": MEDIASTACK_BASE_URL,
    }

    if not API_KEY:
        return {
            "success": False,
            "status": "API key not configured",
            "message": "Please set MEDIASTACK_API_KEY environment variable",
            "signup_url": "https://mediastack.com/signup/free",
            "configuration": status_info,
        }

    # Test API with a simple request
    try:
        test_params = {"keywords": "test", "limit": 1, "offset": 0}

        data = await make_api_request("news", test_params)

        return {
            "success": True,
            "status": "API connection successful",
            "api_response_time": "< 10 seconds",
            "plan_status": "Active",
            "test_query_results": data.get("pagination", {}).get("total", 0),
            "configuration": status_info,
            "message": "Mediastack API is working correctly",
        }

    except ToolError as e:
        return {
            "success": False,
            "status": "API connection failed",
            "error": str(e),
            "configuration": status_info,
            "troubleshooting": [
                "Verify your API key is correct",
                "Check your internet connection",
                "Ensure you haven't exceeded your plan limits",
                "Visit https://mediastack.com/documentation for help",
            ],
        }


# Resources
@mcp.resource("config://mediastack-api")
def get_api_config() -> str:
    """
    Get Mediastack API configuration and setup information.
    """
    return f"""# Mediastack API Configuration

## API Provider
- **Service**: Mediastack News API
- **Website**: https://mediastack.com
- **Plan**: Free Tier (30-minute delay for live news)
- **Documentation**: https://mediastack.com/documentation

## Current Configuration
- **Base URL**: {MEDIASTACK_BASE_URL}
- **API Key**: {'✓ Configured' if API_KEY else '✗ Not Set'}
- **Timeout**: {API_TIMEOUT} seconds

## Setup Instructions
1. Sign up for free at: https://mediastack.com/signup/free
2. Get your API key from the dashboard
3. Set environment variable: MEDIASTACK_API_KEY=your_key_here

## Features Available
- ✅ Live news with 30-minute delay (Free plan)
- ✅ News source discovery
- ✅ Category filtering
- ✅ Country and language filtering
- ✅ Keyword search with exclusions
- ✅ Pagination support
- ❌ Historical news (Requires paid plan)
- ❌ HTTPS access (Requires paid plan)

## Rate Limits
- Free Plan: 1,000 requests/month
- Standard Plan: 10,000 requests/month
- Upgrade at: https://mediastack.com/product
"""


@mcp.resource("data://news-categories")
def get_news_categories() -> str:
    """
    Available news categories with descriptions and examples.
    """
    category_info = {
        "general": "General news and uncategorized articles",
        "business": "Business, finance, and economic news",
        "entertainment": "Entertainment, celebrities, and media news",
        "health": "Health, medical, and wellness news",
        "science": "Scientific discoveries and research news",
        "sports": "Sports news, scores, and athlete updates",
        "technology": "Technology, startups, and innovation news",
    }

    result = "# Available News Categories\n\n"
    for category, description in category_info.items():
        result += f"## {category.title()}\n"
        result += f"- **Description**: {description}\n"
        result += f'- **Usage**: `categories="{category}"`\n'
        result += f'- **Exclude**: `categories="-{category}"`\n\n'

    result += """## Examples
- Single category: `get_news_by_category("technology")`
- Multiple categories: `get_live_news(categories="business,technology")`
- Exclude category: `get_live_news(categories="general,-sports")`
"""

    return result


@mcp.resource("data://supported-countries")
def get_supported_countries() -> str:
    """
    Country codes supported for news filtering.
    """
    result = "# Supported Country Codes\n\n"
    result += "Use these 2-letter country codes for filtering news by region.\n\n"

    for code, name in SUPPORTED_COUNTRIES.items():
        result += f"- **{code.upper()}**: {name}\n"

    result += f"""
## Usage Examples
- Single country: `countries="us"`
- Multiple countries: `countries="us,gb,ca"`
- Exclude country: `countries="us,-gb"`

## Notes
- This list shows common countries
- Mediastack supports many more country codes
- Use ISO 3166-1 alpha-2 format (2 letters)
- Total countries supported: 50+
"""

    return result


@mcp.resource("data://supported-languages")
def get_supported_languages() -> str:
    """
    Language codes supported for news filtering.
    """
    result = "# Supported Language Codes\n\n"
    result += "Use these 2-letter language codes for filtering news by language.\n\n"

    for code, name in SUPPORTED_LANGUAGES.items():
        result += f"- **{code}**: {name}\n"

    result += """
## Usage Examples
- Single language: `languages="en"`
- Multiple languages: `languages="en,es,fr"`
- Exclude language: `languages="en,-de"`

## Notes
- Use ISO 639-1 format (2 letters)
- Most news sources are in English
- Popular languages: en, es, fr, de, zh
"""

    return result


if __name__ == "__main__":
    # Minimal startup output for STDIO compatibility
    try:
        mcp.run()
    except KeyboardInterrupt:
        print("Server stopped")
    except Exception as e:
        print(f"Server error: {e}")
